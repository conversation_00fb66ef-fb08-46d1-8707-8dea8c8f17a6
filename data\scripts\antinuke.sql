-- Antinuke configuration table
CREATE TABLE IF NOT EXISTS antinuke (
    server_id BIGINT PRIMARY KEY,
    enabled BOOLEAN DEFAULT FALSE,
    
    -- Module toggles
    kick_enabled BOOLEAN DEFAULT FALSE,
    ban_enabled BOOLEAN DEFAULT FALSE,
    channel_enabled BOOLEAN DEFAULT FALSE,
    role_enabled BOOLEAN DEFAULT FALSE,
    webhook_enabled BOOLEAN DEFAULT FALSE,
    emoji_enabled BOOLEAN DEFAULT FALSE,
    vanity_enabled BOOLEAN DEFAULT FALSE,
    botadd_enabled BOOLEAN DEFAULT FALSE,
    permissions_enabled BOOLEAN DEFAULT FALSE,
    
    -- Thresholds (actions per minute)
    kick_threshold INTEGER DEFAULT 5,
    ban_threshold INTEGER DEFAULT 3,
    channel_threshold INTEGER DEFAULT 5,
    role_threshold INTEGER DEFAULT 5,
    webhook_threshold INTEGER DEFAULT 3,
    emoji_threshold INTEGER DEFAULT 10,
    
    -- Punishment settings
    kick_punishment TEXT DEFAULT 'kick', -- kick, ban, strip_roles
    ban_punishment TEXT DEFAULT 'ban',
    channel_punishment TEXT DEFAULT 'kick',
    role_punishment TEXT DEFAULT 'kick',
    webhook_punishment TEXT DEFAULT 'ban',
    emoji_punishment TEXT DEFAULT 'kick',
    vanity_punishment TEXT DEFAULT 'ban',
    botadd_punishment TEXT DEFAULT 'kick',
    permissions_punishment TEXT DEFAULT 'kick',
    
    -- Whitelist arrays
    whitelisted_users BIGINT[] DEFAULT '{}',
    whitelisted_bots BIGINT[] DEFAULT '{}',
    
    -- Admin users (can configure antinuke)
    admin_users BIGINT[] DEFAULT '{}',
    
    -- Log channel
    log_channel BIGINT,
    
    -- Creation timestamp
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Antinuke action logs table
CREATE TABLE IF NOT EXISTS antinuke_logs (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    action_type TEXT NOT NULL, -- kick, ban, channel_create, etc.
    timestamp TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    punishment TEXT, -- what punishment was applied
    details JSONB DEFAULT '{}'::jsonb
);

-- Index for faster queries
CREATE INDEX IF NOT EXISTS antinuke_logs_server_time_idx ON antinuke_logs(server_id, timestamp);
CREATE INDEX IF NOT EXISTS antinuke_logs_user_time_idx ON antinuke_logs(user_id, timestamp);
