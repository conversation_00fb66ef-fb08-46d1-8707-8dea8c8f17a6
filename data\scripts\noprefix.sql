-- No-prefix users table
CREATE TABLE IF NOT EXISTS noprefix_users (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    expiry_time TIMESTAMP,  -- NULL means lifetime
    added_by BIGINT,
    added_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Index for faster queries
CREATE INDEX IF NOT EXISTS noprefix_users_user_id_idx ON noprefix_users(user_id);
CREATE INDEX IF NOT EXISTS noprefix_users_expiry_idx ON noprefix_users(expiry_time) WHERE expiry_time IS NOT NULL;
