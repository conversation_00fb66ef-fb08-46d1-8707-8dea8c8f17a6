import click
import asyncio
import logging
import time
import sys

# Add OAuth server import
from utilities.oauth_server import start_oauth_server

# ANSI color codes for styling
BLOOD_RED = "\033[38;5;124m"
DARK_RED = "\033[38;5;88m"
CRIMSON = "\033[38;5;160m"
BRIGHT_RED = "\033[38;5;196m"
RESET = "\033[0m"
DIM = "\033[2m"
BOLD = "\033[1m"

# Custom logger formatter with colors
class ColoredFormatter(logging.Formatter):
    FORMATS = {
        logging.DEBUG: f"{DIM}{{asctime}} {DARK_RED}⚜ [DEBUG]{RESET} {{message}}{RESET}",
        logging.INFO: f"{{asctime}} {CRIMSON}⚜ [INFO]{RESET} {{message}}{RESET}",
        logging.WARNING: f"{{asctime}} {BRIGHT_RED}⚜ [WARNING]{RESET} {{message}}{RESET}",
        logging.ERROR: f"{{asctime}} {BLOOD_RED}⚜ [ERROR]{RESET} {{message}}{RESET}",
        logging.CRITICAL: f"{BOLD}{BLOOD_RED}{{asctime}} ♱ [CRITICAL]{RESET} {{message}}{RESET}",
    }

    def format(self, record):
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt, "%Y-%m-%d %H:%M:%S", style='{')
        return formatter.format(record)

# Configure custom logging
def setup_logging():
    handler = logging.StreamHandler()
    handler.setFormatter(ColoredFormatter())
    
    # Remove all handlers from root logger
    root_logger = logging.getLogger()
    for hdlr in root_logger.handlers[:]:
        root_logger.removeHandler(hdlr)
    
    # Configure root logger with our custom handler
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(handler)
    
    # Silence unnecessary discord warnings
    logging.getLogger('discord').setLevel(logging.ERROR)
    logging.getLogger('discord.http').setLevel(logging.ERROR)
    logging.getLogger('discord.gateway').setLevel(logging.WARNING)

@click.command()
@click.argument("mode", default="production")
def main(mode):
    """Launches the bot."""
    mode = mode.lower()
    
    # Set up custom logging
    setup_logging()
    
    # Display mode banner
    block = f"{BLOOD_RED}{'═' * (len(mode) + 19)}{RESET}"
    startmsg = f"{block}\n{BLOOD_RED}═══ Running {CRIMSON}{mode.capitalize()}{BLOOD_RED} Mode ═══{RESET}\n{block}"
    click.echo(startmsg)
    
    # Import bot after logging setup
    from core import bot
    logger = logging.getLogger('bot_starter')

    if mode in ["dev", "development"]:
        bot.development = True
        logger.info(f"Starting bot in {CRIMSON}Development{RESET} mode")
    elif mode in ["tester", "testing"]:
        bot.tester = True
        logger.info(f"Starting bot in {CRIMSON}Testing{RESET} mode")
    else:
        bot.production = True
        logger.info(f"Starting bot in {CRIMSON}Production{RESET} mode")
    
    logger.info("Attempting to start bot...")
    
    # Add event handler to bot for on_ready
    @bot.event
    async def on_ready():
        logger.info(f"Bot {CRIMSON}{bot.user.name}{RESET} has successfully logged in!")
        logger.info(f"Connected to {CRIMSON}{len(bot.guilds)}{RESET} servers")
        
        # Start OAuth server after bot is ready
        try:
            bot.oauth_runner = await start_oauth_server()
            logger.info(f"OAuth server started at {CRIMSON}http://127.0.0.1:8080{RESET}")
        except Exception as e:
            logger.error(f"Failed to start OAuth server: {e}")
    
    # Start the bot - checking if run is a coroutine or not
    if asyncio.iscoroutinefunction(bot.run):
        # If it's a coroutine, run it with asyncio
        asyncio.run(bot.run())
    else:
        # If it's a regular function, just call it
        bot.run()

if __name__ == "__main__":
    main()




