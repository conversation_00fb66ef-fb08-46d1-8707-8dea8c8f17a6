import discord
from discord.ext import commands
import asyncio
from datetime import datetime
import random

TICKET_ROLE_ID = 1402926443800821761  # Replace with your mod role ID
TICKET_CATEGORY_ID = 123456789012345678  # Replace with ticket category ID (optional)

class Ticket(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.panel_message_id = None
        self.active_tickets = {}  # Store active tickets {user_id: thread_id}

    @commands.command()
    @commands.has_permissions(administrator=True)
    async def ticketpanel(self, ctx):
        """Send the ticket panel."""
        embed = discord.Embed(
            title="Contact Support",
            description="Select an option below to open a private ticket with our support team.",
            color=0x323339
        )

        embed.add_field(
            name="Available Support Options:",
            value="• Buy - Purchase inquiries and billing support\n• Code Support - Technical assistance and troubleshooting\n• Talk to Owner - Direct communication with management",
            inline=False
        )

        embed.add_field(
            name="Important",
            value="**Please select the most appropriate option for your inquiry to ensure faster response times.**",
            inline=False
        )

        view = TicketPanelView(self)
        msg = await ctx.send(embed=embed, view=view)
        self.panel_message_id = msg.id

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def close(self, ctx):
        """Close a ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("This command can only be used in ticket threads.")

        if not ctx.channel.name.startswith("ticket-"):
            return await ctx.send("This doesn't appear to be a ticket thread.")

        # Archive and lock the thread
        await ctx.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {ctx.channel.name}"
            await ctx.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets
        user_id = None
        for uid, thread_id in self.active_tickets.items():
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        embed = discord.Embed(
            description="This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x323339
        )
        await ctx.send(embed=embed)

    @commands.command()
    @commands.has_permissions(manage_messages=True)
    async def delete(self, ctx):
        """Delete a ticket thread."""
        if not isinstance(ctx.channel, discord.Thread):
            return await ctx.send("This command can only be used in ticket threads.")

        if not ctx.channel.name.startswith("ticket-"):
            return await ctx.send("This doesn't appear to be a ticket thread.")

        # Remove from active tickets
        user_id = None
        for uid, thread_id in self.active_tickets.items():
            if thread_id == ctx.channel.id:
                user_id = uid
                break

        if user_id:
            del self.active_tickets[user_id]

        # Send confirmation message before deletion
        embed = discord.Embed(
            description="This ticket thread will be deleted in 5 seconds...",
            color=0x323339
        )
        await ctx.send(embed=embed)

        # Wait 5 seconds then delete
        await asyncio.sleep(5)
        await ctx.channel.delete()

class TicketPanelView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.select(
        placeholder="Select a support option...",
        min_values=1,
        max_values=1,
        options=[
            discord.SelectOption(
                label="Buy",
                description="Purchase inquiries and billing support",
                value="buy"
            ),
            discord.SelectOption(
                label="Code Support",
                description="Technical assistance and troubleshooting",
                value="code_support"
            ),
            discord.SelectOption(
                label="Talk to Owner",
                description="Direct communication with management",
                value="talk_to_owner"
            )
        ],
        custom_id="ticket_select"
    )
    async def create_ticket(self, interaction: discord.Interaction, select: discord.ui.Select):
        # Get the selected option
        selected_option = select.values[0]

        # Check if user already has an active ticket
        if interaction.user.id in self.cog.active_tickets:
            existing_thread_id = self.cog.active_tickets[interaction.user.id]
            try:
                existing_thread = interaction.guild.get_thread(existing_thread_id)
                if existing_thread and not existing_thread.archived:
                    return await interaction.response.send_message(
                        f"You already have an active ticket: {existing_thread.mention}",
                        ephemeral=True
                    )
                else:
                    # Clean up old reference
                    del self.cog.active_tickets[interaction.user.id]
            except:
                # Thread doesn't exist anymore, clean up
                del self.cog.active_tickets[interaction.user.id]

        guild = interaction.guild
        mod_role = guild.get_role(TICKET_ROLE_ID)

        # Generate unique ticket ID and create thread name based on selection
        ticket_id = random.randint(1000, 9999)
        thread_name = f"ticket-{selected_option}-{ticket_id}"

        try:
            # Create a private thread directly from the channel
            thread = await interaction.channel.create_thread(
                name=thread_name,
                type=discord.ChannelType.private_thread,
                auto_archive_duration=4320,  # 3 days
                reason=f"Ticket created by {interaction.user}"
            )
            
            # Add the user to the thread
            await thread.add_user(interaction.user)
            
            # Store the active ticket
            self.cog.active_tickets[interaction.user.id] = thread.id
            
            # Send initial message in thread
            embed = discord.Embed(
                title=f"Ticket opened!",
                description=f"Your ticket has been created at **{thread_name}**. A moderator will assist you shortly.",
                color=0x323339,
                timestamp=datetime.now(datetime.timezone.utc)
            )

            await thread.send(f"{interaction.user.mention}")

            # Send the main ticket message with new format
            ticket_embed = discord.Embed(
                description="• our support team have been notified to come help !\n\n• whilst you are waiting, please write any additional information, such as:\n\n• any screenshots relating to your issue\n• any commands you ran, if applicable\n\nthank you for your patience!\n\nplease avoid pinging roles or staff members: we will come to help you as soon as we can.",
                color=0x323339
            )

            # Add close button
            close_view = TicketCloseView(self.cog)
            await thread.send(embed=ticket_embed, view=close_view)
            
            # Notify moderators (without ping to avoid spam)
            if mod_role:
                mod_embed = discord.Embed(
                    description=f"**{interaction.user}** has created a {selected_option.replace('_', ' ').title()} support ticket.",
                    color=0x323339,
                    timestamp=datetime.now(datetime.timezone.utc)
                )
                mod_embed.add_field(
                    name="User ID",
                    value=f"`{interaction.user.id}`",
                    inline=False
                )
                await thread.send(embed=mod_embed)

            # Respond to the interaction
            await interaction.response.send_message(embed=embed, ephemeral=True)

        except discord.Forbidden:
            await interaction.response.send_message(
                "I don't have permission to create threads in this channel.",
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.response.send_message(
                f"Failed to create ticket: {str(e)}",
                ephemeral=True
            )

class TicketCloseView(discord.ui.View):
    def __init__(self, cog):
        super().__init__(timeout=None)
        self.cog = cog

    @discord.ui.button(label="close thread", style=discord.ButtonStyle.danger, custom_id="close_ticket")
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        # Only allow the ticket creator or moderators to close
        if (interaction.user.id not in [member.id for member in interaction.channel.members] or
            not any(role.id == TICKET_ROLE_ID for role in interaction.user.roles)):
            return await interaction.response.send_message(
                "You don't have permission to close this ticket.",
                ephemeral=True
            )

        # Archive and lock the thread
        await interaction.channel.edit(archived=True, locked=True)

        # Update channel name to show it's resolved
        try:
            new_name = f"[Resolved] {interaction.channel.name}"
            await interaction.channel.edit(name=new_name)
        except:
            pass

        # Remove from active tickets
        user_id = None
        for uid, thread_id in self.cog.active_tickets.items():
            if thread_id == interaction.channel.id:
                user_id = uid
                break

        if user_id:
            del self.cog.active_tickets[user_id]

        embed = discord.Embed(
            description="This ticket has been locked and archived. Still need help? Create another ticket in the main channel.",
            color=0x323339
        )
        await interaction.response.send_message(embed=embed)

async def setup(bot):
    await bot.add_cog(Ticket(bot))

    # Add persistent views
    bot.add_view(TicketPanelView(bot.get_cog('Ticket')))
    bot.add_view(TicketCloseView(bot.get_cog('Ticket')))