import discord
import os
import io
import asyncio
import threading
import psutil
import sys
import functools
import objgraph
import traceback

from discord.ext import commands, menus

from utilities import checks
from utilities import decorators
from utilities import pagination


async def setup(bot):
    await bot.add_cog(Monitor(bot))


class Monitor(commands.Cog):
    """
    Module for monitoring bot status.
    """

    def __init__(self, bot):
        self.bot = bot
        self.process = psutil.Process()
        self.todo_list = "./data/txts/todo.txt"

    # Admin only cog.
    async def cog_check(self, ctx):
        return checks.is_admin(ctx)

    @decorators.command(
        aliases=["objg"],
        brief="Debug memory leaks.",
        implemented="2021-05-11 01:47:43.865390",
        updated="2021-05-11 01:47:43.865390",
    )
    async def objgrowth(self, ctx):
        """
        Usage: {0}objgrowth
        Alias: {0}objg
        Output:
            Shows detailed object memory usage
        """
        stdout = io.StringIO()
        await ctx.bot.loop.run_in_executor(
            None, functools.partial(objgraph.show_growth, file=stdout)
        )
        await ctx.send_or_reply("```fix\n" + stdout.getvalue() + "```")

    @decorators.group(
        case_insensitive=True,
        aliases=["to-do"],
        invoke_without_command=True,
        brief="Manage the bot's todo list.",
    )
    async def todo(self, ctx):
        """
        Usage: {0}todo <method>
        Alias: {0}to-do
        Methods:
            no subcommand: shows the todo list
            add: Adds an entry to the todo list
            remove|rm|rem: Removes an entry from the todo list
        """
        if ctx.invoked_subcommand is None:
            try:
                with open(self.todo_list) as fp:
                    data = fp.readlines()
            except FileNotFoundError:
                return await ctx.send_or_reply(
                    f"{self.bot.emote_dict['exclamation']} No current todos."
                )
            if data is None or data == "":
                return await ctx.send_or_reply(
                    f"{self.bot.emote_dict['exclamation']} No current todos."
                )
            msg = ""
            for index, line in enumerate(data, start=1):
                msg += f"{index}. {line}\n"
            p = pagination.MainMenu(
                pagination.TextPageSource(msg, prefix="```prolog\n")
            )
            try:
                await p.start(ctx)
            except menus.MenuError as e:
                await ctx.send_or_reply(e)

    @todo.command(brief="Add a todo entry.")
    async def add(self, ctx, *, todo: str = None):
        if todo is None:
            return await ctx.send_or_reply(
                content=f"Usage: `{ctx.clean_prefix}todo add <todo>`",
            )
        with open(self.todo_list, "a", encoding="utf-8") as fp:
            fp.write(todo + "\n")
        await ctx.success(f"Successfully added `{todo}` to the todo list.")

    @todo.command(aliases=["rm", "rem"], brief="Remove a todo entry.")
    async def remove(self, ctx, *, index_or_todo: str = None):
        with open(self.todo_list, mode="r", encoding="utf-8") as fp:
            lines = fp.readlines()
        found = False
        for index, line in enumerate(lines, start=1):
            if str(index) == index_or_todo:
                lines.remove(line)
                found = True
                break
            elif line.lower().strip("\n") == index_or_todo.lower():
                lines.remove(line)
                found = True
                break
        if found is True:
            with open(self.todo_list, mode="w", encoding="utf-8") as fp:
                fp.write("".join(lines))
            await ctx.success(
                f"Successfully removed todo `{index_or_todo}` from the todo list."
            )
        else:
            await ctx.fail(f"Could not find todo `{index_or_todo}` in the todo list.")

    @todo.command(brief="Clear the todo list.")
    async def clear(self, ctx):
        try:
            os.remove(self.todo_list)
        except FileNotFoundError:
            return await ctx.success("Successfully cleared the todo list.")
        await ctx.success("Successfully cleared the todo list.")

    @decorators.group(
        aliases=["l"],
        invoke_without_command=True,
        brief="View logging files.",
        implemented="2021-05-08 20:16:22.917120",
        updated="2021-05-11 02:51:30.992778",
    )
    async def logger(self, ctx):
        """
        Usage: {0}logger <option>
        Alias: {0}l
        Permission: Bot owner
        Output: View any log recorded in ./data/logs
        Options:
            commands|command|cmds|cmd
            errors|stderr|error|err|e
            information|info|i
            tracebacks|traceback|trace|tb|t
            clear|clr|cl
        """
        if not ctx.invoked_subcommand:
            return await ctx.usage("<option>")

    @logger.command(
        name="commands", aliases=["cmds"], brief="Show the commands.log file."
    )
    async def _get_cmds(self, ctx):
        """
        Usage: {0}logger commands
        Aliases: {0}logger cmds
        Output:
            Starts a pagination session
            showing the commands.log file
        """
        sh = self.bot.get_command("sh")
        await ctx.invoke(sh, prefix="prolog", command="cat ./data/logs/commands.log")

    @logger.command(
        name="traceback",
        aliases=["tracebacks", "trace", "t", "tb"],
        brief="Show the traceback.log file",
    )
    async def _traceback(self, ctx):
        """
        Usage: {0}logger traceback
        Aliases:
            {0}logger t
            {0}logger tb
            {0}logger trace
            {0}logger tracebacks
        Output:
            Starts a pagination session
            showing the traceback.log file
        """
        sh = self.bot.get_command("sh")
        await ctx.invoke(sh, prefix="prolog", command="cat ./data/logs/traceback.log")

    @logger.command(
        name="info",
        aliases=["i", "information"],
        brief="Show the info.log file.",
    )
    async def _info(self, ctx):
        """
        Usage: {0}logger info
        Aliases:
            {0}logger i
            {0}logger information
        Output:
            Starts a pagination session
            showing the info.log file
        """
        sh = self.bot.get_command("sh")
        await ctx.invoke(sh, prefix="prolog", command="cat ./data/logs/info.log")

    @logger.command(
        name="errors",
        aliases=["err", "error", "stderr", "e"],
        brief="Show the errors.log file.",
    )
    async def _errors(self, ctx):
        """
        Usage: {0}logger errors
        Aliases:
            {0}logger e
            {0}logger err
            {0}logger error
            {0}logger stderr
        Output:
            Starts a pagination session
            showing the errors.log file
        """
        sh = self.bot.get_command("sh")
        await ctx.invoke(sh, prefix="prolog", command="cat ./data/logs/errors.log")

    @logger.command(
        name="music",
        aliases=["m", "audio", "voice"],
        brief="Show the music.log file.",
    )
    async def _music(self, ctx):
        """
        Usage: {0}logger errors
        Aliases:
            {0}logger m
            {0}logger music
            {0}logger audio
            {0}logger voice
        Output:
            Starts a pagination session
            showing the errors.log file
        """
        sh = self.bot.get_command("sh")
        await ctx.invoke(sh, prefix="prolog", command="cat ./data/logs/music.log")

    @logger.command(
        name="clear",
        aliases=["cl", "clr"],
        brief="Delete a logging file.",
    )
    async def _cl(self, ctx, search):
        """
        Usage: {0}logger clear <search>
        Aliases: {0}cl, {0}clr
        Output: Deletes a logging file.
        Searches:
            commands|command|cmds|cmd
            errors|stderr|error|err
            information|info|i
            tracebacks|traceback|trace|t
        """
        if search in ["cmd", "cmds", "command", "commands"]:
            search = "commands"
            msg = "command"
        elif search in ["err", "stderr", "error", "errors"]:
            search = "errors"
            msg = "error"
        elif search in ["info", "i", "information"]:
            search = "info"
            msg = "info"
        elif search in ["t", "trace", "traceback", "tracebacks"]:
            search = "traceback"
            msg = "traceback"
        elif search in ["m", "audio", "music", "voice"]:
            search = "music"
            msg = "music"
        else:
            raise commands.BadArgument(f"Invalid file search.")
        logdir = os.listdir("./data/logs/")
        for filename in logdir:
            if filename.startswith(search):
                os.remove("./data/logs/" + filename)
                break
        await ctx.success(f"Cleared the {msg} log file.")

    @decorators.group(
        brief="View pm2 files.",
        case_insensitive=True,
        invoke_without_command=True,
        implemented="2021-05-08 20:16:22.917120",
        updated="2021-05-11 02:51:30.992778",
    )
    async def pm2(self, ctx):
        """
        Usage: {0}pm2 <option>
        Output: View any pm2 log file in ./data/pm2
        Options:
            stdout|out|output
            stderr|err|error|errors
            pid|process|processid
            clear|clr|cl
        """
        if ctx.invoked_subcommand is None:
            return await ctx.usage("<option>")

    @pm2.command(aliases=["out", "output"], brief="View the pm2 stdout file.")
    async def stdout(self, ctx):
        """
        Usage: {0}pm2 stdout
        Aliases:
            {0}pm2 out
            {0}pm2 output
        Output:
            Starts a pagination session
            showing the pm2 stdout file.
        """
        sh = self.bot.get_command("sh")
        pm2dir = os.listdir("./data/pm2/")
        for filename in pm2dir:
            if filename.startswith("out"):
                await ctx.invoke(sh, prefix="yml", command=f"cat ./data/pm2/{filename}")
                return
        else:
            raise commands.BadArgument(f"No stdout file currently exists.")

    @pm2.command(
        aliases=["err", "error", "errors"],
        brief="View the pm2 stderr file",
    )
    async def stderr(self, ctx):
        """
        Usage: {0}pm2 stderr
        Aliases:
            {0}pm2 err
            {0}pm2 error
            {0}pm2 errors
        Output:
            Starts a pagination session
            showing the pm2 stderr file.
        """
        sh = self.bot.get_command("sh")
        pm2dir = os.listdir("./data/pm2/")
        for filename in pm2dir:
            if filename.startswith("err"):
                await ctx.invoke(sh, prefix="yml", command=f"cat ./data/pm2/{filename}")
                return
        else:
            raise commands.BadArgument(f"No stderr file currently exists.")

    @pm2.command(
        aliases=["process", "processid"],
        brief="View the pm2 process ID.",
    )
    async def pid(self, ctx):
        """
        Usage: {0}pm2 pid
        Aliases:
            {0}pm2 process
            {0}pm2 processid
        Output:
            Shows the process ID
            of the pm2 process.
        """
        sh = self.bot.get_command("sh")
        pm2dir = os.listdir("./data/pm2/")
        for filename in pm2dir:
            if filename.startswith("pid"):
                await ctx.invoke(sh, prefix="yml", command=f"cat ./data/pm2/{filename}")
                return
        else:
            raise commands.BadArgument(f"No pid file currently exists.")

    @pm2.command(name="clear", aliases=["cl", "clr"], brief="Delete a pm2 log file.")
    async def _clr(self, ctx, search):
        """
        Usage: {0}pm2 clear <search>
        Aliases: {0}cl, {0}clr
        Output: Deletes a pm2 file.
        Searches:
            out|output|stdout
            errors|stderr|error|err
            pid|process|processid
        """
        if search in ["out", "output", "stdout"]:
            search = "out"
            msg = "stdout"
        elif search in ["err", "stderr", "error", "errors"]:
            search = "err"
            msg = "stderr"
        elif search in ["pid", "p", "process", "processid"]:
            search = "pid"
            msg = "pid"
        else:
            raise commands.BadArgument(f"Invalid file search.")
        pm2dir = os.listdir("./data/pm2/")
        for filename in pm2dir:
            if filename.startswith(search):
                os.remove("./data/pm2/" + filename)
                break
        await ctx.success(f"Cleared the pm2 {msg} log.")

    @decorators.command(brief="Show bot threadinfo.")
    async def threadinfo(self, ctx):
        """
        Usage: {0}threadinfo
        Output:
            Shows some info on bot threads.
        """
        buf = io.StringIO()
        for th in threading.enumerate():
            buf.write(str(th) + "\n")
            traceback.print_stack(sys._current_frames()[th.ident], file=buf)
            buf.write("\n")

        p = pagination.MainMenu(
            pagination.TextPageSource(buf.getvalue(), prefix="```prolog")
        )
        try:
            await p.start(ctx)
        except menus.MenuError as e:
            await ctx.send_or_reply(e)

    @decorators.command(brief="Show bot and Lavalink health status.")
    async def bothealth(self, ctx):
        """
        Usage: {0}bothealth
        Output:
            Shows Lavalink, bot connection, and process health in a simple paginated menu.
        """

        lavalink_status = "Not connected"
        lavalink_stats = {
            "ws_ping": "N/A",
            "playing_players": "N/A",
            "uptime": "N/A",
            "memory": "N/A",
            "cpu": "N/A"
        }

        try:
            import wavelink
            
            # Check if Pool has nodes
            if wavelink.Pool.nodes:
                # Get the first node
                node = next(iter(wavelink.Pool.nodes.values()))
                
                # Check node status first
                if hasattr(node, 'status') and str(node.status) == "NodeStatus.CONNECTED":
                    lavalink_status = "Connected"
                    
                    # Try to fetch stats using the fetch_stats method
                    try:
                        stats = await node.fetch_stats()
                        print(f"[DEBUG] Fetched stats: {stats}")
                        print(f"[DEBUG] Stats type: {type(stats)}")
                        print(f"[DEBUG] Stats attributes: {[attr for attr in dir(stats) if not attr.startswith('_')]}")
                        
                        if stats:
                            # Try different ping attribute names
                            ping_attrs = ['ping', 'ws_ping', 'websocket_ping', 'latency']
                            for attr in ping_attrs:
                                if hasattr(stats, attr):
                                    ping_value = getattr(stats, attr)
                                    lavalink_stats["ws_ping"] = f"{float(ping_value):.2f}"
                                    print(f"[DEBUG] Found ping: {attr} = {ping_value}")
                                    break
                            
                            # Try players
                            players_attrs = ['playing_players', 'players', 'active_players', 'playingPlayers']
                            for attr in players_attrs:
                                if hasattr(stats, attr):
                                    lavalink_stats["playing_players"] = str(getattr(stats, attr))
                                    print(f"[DEBUG] Found players: {attr} = {getattr(stats, attr)}")
                                    break
                            
                            # Try uptime
                            uptime_attrs = ['uptime', 'node_uptime']
                            for attr in uptime_attrs:
                                if hasattr(stats, attr):
                                    uptime_val = getattr(stats, attr)
                                    lavalink_stats["uptime"] = f"{uptime_val // 1000}s"
                                    print(f"[DEBUG] Found uptime: {attr} = {uptime_val}")
                                    break
                            
                            # Try memory - check multiple formats
                            if hasattr(stats, 'memory_used') and hasattr(stats, 'memory_allocated'):
                                memory_used = getattr(stats, 'memory_used')
                                memory_allocated = getattr(stats, 'memory_allocated')
                                lavalink_stats["memory"] = (
                                    f"{memory_used // 1024 // 1024} MB / "
                                    f"{memory_allocated // 1024 // 1024} MB"
                                )
                                print(f"[DEBUG] Found memory: used={memory_used}, allocated={memory_allocated}")
                            elif hasattr(stats, 'memory'):
                                mem = getattr(stats, 'memory')
                                print(f"[DEBUG] Memory object: {mem}, type: {type(mem)}")
                                if hasattr(mem, 'used') and hasattr(mem, 'allocated'):
                                    lavalink_stats["memory"] = (
                                        f"{mem.used // 1024 // 1024} MB / "
                                        f"{mem.allocated // 1024 // 1024} MB"
                                    )
                                elif hasattr(mem, 'usable') and hasattr(mem, 'allocated'):
                                    lavalink_stats["memory"] = (
                                        f"{mem.usable // 1024 // 1024} MB / "
                                        f"{mem.allocated // 1024 // 1024} MB"
                                    )
                            
                            # Try CPU
                            if hasattr(stats, 'cpu_system_load') and hasattr(stats, 'cpu_lavalink_load'):
                                cpu_system = getattr(stats, 'cpu_system_load')
                                cpu_lavalink = getattr(stats, 'cpu_lavalink_load')
                                lavalink_stats["cpu"] = (
                                    f"System: {cpu_system * 100:.2f}% | "
                                    f"Lavalink: {cpu_lavalink * 100:.2f}%"
                                )
                                print(f"[DEBUG] Found CPU: system={cpu_system}, lavalink={cpu_lavalink}")
                            elif hasattr(stats, 'cpu'):
                                cpu = getattr(stats, 'cpu')
                                print(f"[DEBUG] CPU object: {cpu}, type: {type(cpu)}")
                                if hasattr(cpu, 'systemLoad') and hasattr(cpu, 'lavalinkLoad'):
                                    lavalink_stats["cpu"] = (
                                        f"System: {cpu.systemLoad * 100:.2f}% | "
                                        f"Lavalink: {cpu.lavalinkLoad * 100:.2f}%"
                                    )
                                elif hasattr(cpu, 'system_load') and hasattr(cpu, 'lavalink_load'):
                                    lavalink_stats["cpu"] = (
                                        f"System: {cpu.system_load * 100:.2f}% | "
                                        f"Lavalink: {cpu.lavalink_load * 100:.2f}%"
                                    )
                        else:
                            print("[DEBUG] fetch_stats returned None")
                            
                    except Exception as e:
                        print(f"[DEBUG] fetch_stats failed: {e}")
                        lavalink_status = "Connected (stats unavailable)"
                        
                        # Fallback: try to get basic info
                        if hasattr(node, 'ping'):
                            lavalink_stats["ws_ping"] = f"{node.ping:.2f}"
                        
                        # Count players manually
                        try:
                            if hasattr(node, 'players'):
                                player_count = len([p for p in node.players.values() if p and hasattr(p, 'guild')])
                                lavalink_stats["playing_players"] = str(player_count)
                        except:
                            pass
                        
                elif hasattr(node, 'status'):
                    lavalink_status = f"Status: {node.status}"
                else:
                    lavalink_status = "Unknown status"
            else:
                lavalink_status = "No nodes configured"
                
        except ImportError:
            lavalink_status = "Wavelink not available"
        except Exception as e:
            lavalink_status = f"Error: {str(e)[:50]}"
            print(f"[DEBUG] Exception: {e}")
            import traceback
            traceback.print_exc()

        # Bot connection status
        bot_status = "Connected" if not ctx.bot.is_closed() else "Disconnected"
        latency = ctx.bot.latency * 1000 if ctx.bot.latency else 0

        # Process info
        process = psutil.Process()

        view = HealthView(ctx.bot, lavalink_status, lavalink_stats, bot_status, latency, process)
        embed = view.pages[0]
        msg = await ctx.send(embed=embed, view=view)
        view.message = msg


class HealthView(discord.ui.View):
    def __init__(self, bot, lavalink_status, lavalink_stats, bot_status, latency, process):
        super().__init__(timeout=60)
        self.bot = bot
        self.pages = []
        self.page = 0

        # Lavalink page
        lavalink_desc = (
            f"**Status:** {lavalink_status}\n"
            f"**WebSocket Ping:** {lavalink_stats.get('ws_ping', 'N/A')} ms\n"
            f"**Active Players:** {lavalink_stats.get('playing_players', 'N/A')}\n"
            f"**Uptime:** {lavalink_stats.get('uptime', 'N/A')}\n"
            f"**Memory Usage:** {lavalink_stats.get('memory', 'N/A')}\n"
            f"**CPU Load:** {lavalink_stats.get('cpu', 'N/A')}\n"
        )
        self.pages.append(
            discord.Embed(
                title="Lavalink Node Status",
                description=lavalink_desc,
                color=0x1CA381 if lavalink_status == "Connected" else 0xF04947,
            )
        )

        # Bot connection page
        bot_desc = (
            f"**Status:** {bot_status}\n"
            f"**WebSocket Latency:** {latency:.2f} ms\n"
        )
        self.pages.append(
            discord.Embed(
                title="Bot Connection Status",
                description=bot_desc,
                color=0x1CA381 if bot_status == "Connected" else 0xF04947,
            )
        )

        # Process info page
        mem = process.memory_full_info().uss / 1024 ** 2
        cpu = process.cpu_percent() / psutil.cpu_count()
        proc_desc = (
            f"**Memory Usage:** {mem:.2f} MB\n"
            f"**CPU Usage:** {cpu:.2f}%"
        )
        self.pages.append(
            discord.Embed(
                title="Bot Process Info",
                description=proc_desc,
                color=0x1CA381,
            )
        )

        # Navigation
        if len(self.pages) > 1:
            prev_btn = discord.ui.Button(label="Previous", style=discord.ButtonStyle.secondary)
            next_btn = discord.ui.Button(label="Next", style=discord.ButtonStyle.secondary)
            prev_btn.callback = self.prev_page
            next_btn.callback = self.next_page
            self.add_item(prev_btn)
            self.add_item(next_btn)

    async def prev_page(self, interaction: discord.Interaction):
        if self.page > 0:
            self.page -= 1
            await interaction.response.edit_message(embed=self.pages[self.page], view=self)
        else:
            await interaction.response.defer()

    async def next_page(self, interaction: discord.Interaction):
        if self.page < len(self.pages) - 1:
            self.page += 1
            await interaction.response.edit_message(embed=self.pages[self.page], view=self)
        else:
            await interaction.response.defer()

    async def on_timeout(self):
        for item in self.children:
            item.disabled = True
        try:
            await self.message.edit(view=self)
        except Exception:
            pass