-- Last.fm integration tables

-- User Last.fm accounts
CREATE TABLE IF NOT EXISTS lastfm_users (
    user_id BIGINT PRIMARY KEY,
    lastfm_username TEXT NOT NULL,
    connected_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC'),
    last_updated TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);

-- Last.fm user settings and preferences
CREATE TABLE IF NOT EXISTS lastfm_settings (
    user_id BIGINT PRIMARY KEY REFERENCES lastfm_users(user_id) ON DELETE CASCADE,
    embed_color TEXT DEFAULT '#323339',
    now_playing_mode TEXT DEFAULT 'default', -- 'default', 'minimal', 'detailed'
    custom_reactions JSONB DEFAULT '[]'::JSONB,
    auto_update BOOLEAN DEFAULT TRUE,
    privacy_mode BOOLEAN DEFAULT FALSE
);

-- Server-wide Last.fm settings
CREATE TABLE IF NOT EXISTS lastfm_server_settings (
    server_id BIGINT PRIMARY KEY,
    reactions_enabled BOOLEAN DEFAULT TRUE,
    upvote_emoji TEXT DEFAULT '👍',
    downvote_emoji TEXT DEFAULT '👎',
    whoknows_limit INTEGER DEFAULT 10,
    crown_threshold INTEGER DEFAULT 30 -- Minimum plays to get a crown
);

-- Artist crowns (user with most plays for an artist in a server)
CREATE TABLE IF NOT EXISTS lastfm_crowns (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    artist_name TEXT NOT NULL,
    playcount INTEGER NOT NULL DEFAULT 0,
    last_updated TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS lastfm_crowns_idx ON lastfm_crowns(server_id, artist_name);

-- Last.fm reaction scores (for scoreboard)
CREATE TABLE IF NOT EXISTS lastfm_reactions (
    id BIGSERIAL PRIMARY KEY,
    server_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    message_id BIGINT NOT NULL,
    reaction_type TEXT NOT NULL, -- 'upvote' or 'downvote'
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS lastfm_reactions_idx ON lastfm_reactions(server_id, user_id, message_id, reaction_type);

-- Custom album artwork submissions
CREATE TABLE IF NOT EXISTS lastfm_artwork (
    id BIGSERIAL PRIMARY KEY,
    artist_name TEXT NOT NULL,
    album_name TEXT NOT NULL,
    image_url TEXT NOT NULL,
    submitted_by BIGINT NOT NULL,
    votes INTEGER DEFAULT 0,
    approved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS lastfm_artwork_idx ON lastfm_artwork(artist_name, album_name, submitted_by);

-- Artwork votes
CREATE TABLE IF NOT EXISTS lastfm_artwork_votes (
    id BIGSERIAL PRIMARY KEY,
    artwork_id BIGINT REFERENCES lastfm_artwork(id) ON DELETE CASCADE,
    user_id BIGINT NOT NULL,
    vote_type TEXT NOT NULL, -- 'upvote' or 'downvote'
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS lastfm_artwork_votes_idx ON lastfm_artwork_votes(artwork_id, user_id);

-- Cached user data for performance (optional)
CREATE TABLE IF NOT EXISTS lastfm_cache (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    cache_type TEXT NOT NULL, -- 'top_artists', 'top_tracks', 'recent_tracks', etc.
    period TEXT DEFAULT 'overall', -- time period for the cache
    data JSONB NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC')
);
CREATE UNIQUE INDEX IF NOT EXISTS lastfm_cache_idx ON lastfm_cache(user_id, cache_type, period);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS lastfm_crowns_server_idx ON lastfm_crowns(server_id);
CREATE INDEX IF NOT EXISTS lastfm_crowns_user_idx ON lastfm_crowns(user_id);
CREATE INDEX IF NOT EXISTS lastfm_reactions_server_idx ON lastfm_reactions(server_id);
CREATE INDEX IF NOT EXISTS lastfm_reactions_user_idx ON lastfm_reactions(user_id);
CREATE INDEX IF NOT EXISTS lastfm_cache_expires_idx ON lastfm_cache(expires_at);
