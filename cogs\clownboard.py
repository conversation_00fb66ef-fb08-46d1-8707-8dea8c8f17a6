import discord
import typing
from discord.ext import commands
from collections import defaultdict

from utilities import checks
from utilities import helpers
from utilities import converters
from utilities import decorators


async def setup(bot):
    await bot.add_cog(Clownboard(bot))


class Clownboard(commands.Cog):
    """
    Showcase the worst messages in your server
    """

    def __init__(self, bot):
        self.bot = bot
        self.clownboard_cache = defaultdict(dict)  # Cache for clownboard configs
        self.ignored_cache = defaultdict(set)  # Cache for ignored entities
        bot.loop.create_task(self.load_clownboard_configs())

    async def load_clownboard_configs(self):
        """Load clownboard configurations from database"""
        if not self.bot.cxn:
            return
        try:
            # Load configs
            query = """
                    SELECT * FROM clownboard_config;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                self.clownboard_cache[record['server_id']] = dict(record)

            # Load ignored entities
            query = """
                    SELECT server_id, entity_id, entity_type
                    FROM clownboard_ignored;
                    """
            records = await self.bot.cxn.fetch(query)
            for record in records:
                self.ignored_cache[record['server_id']].add(
                    (record['entity_id'], record['entity_type'])
                )
        except Exception as e:
            print(f"Failed to load clownboard configs: {e}")

    async def get_clownboard_config(self, guild_id):
        """Get clownboard config for a guild"""
        if guild_id not in self.clownboard_cache:
            query = """
                    SELECT * FROM clownboard_config
                    WHERE server_id = $1;
                    """
            record = await self.bot.cxn.fetchrow(query, guild_id)
            if record:
                self.clownboard_cache[guild_id] = dict(record)
            else:
                return None
        return self.clownboard_cache.get(guild_id)

    async def create_clownboard_config(self, guild_id):
        """Create default clownboard config"""
        query = """
                INSERT INTO clownboard_config (server_id)
                VALUES ($1)
                ON CONFLICT (server_id) DO NOTHING;
                """
        await self.bot.cxn.execute(query, guild_id)
        self.clownboard_cache[guild_id] = {
            'server_id': guild_id,
            'channel_id': None,
            'emoji': '🤡',
            'threshold': 3,
            'color': '#323339',
            'locked': False,
            'allow_selfstar': False,
            'show_attachments': True,
            'show_jumpurl': True,
            'show_timestamp': True
        }

    def is_ignored(self, guild_id, entity_id, entity_type):
        """Check if an entity is ignored"""
        return (entity_id, entity_type) in self.ignored_cache.get(guild_id, set())

    @decorators.group(
        invoke_without_command=True,
        brief="Showcase the worst messages in your server",
        implemented="2024-07-20 00:00:00.000000",
        updated="2024-07-20 00:00:00.000000",
    )
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard(self, ctx):
        """
        Usage: {0}clownboard
        Permission: Manage Guild
        Output:
            Shows the current clownboard configuration
            for this server.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            return await ctx.fail("Clownboard is not configured for this server. Use `clownboard set` to configure it.")

        embed = discord.Embed(
            title="Clownboard Configuration",
            color=int(config['color'].replace('#', ''), 16) if config['color'] else 0x323339
        )

        channel = ctx.guild.get_channel(config['channel_id']) if config['channel_id'] else None
        embed.add_field(
            name="Channel",
            value=channel.mention if channel else "Not set",
            inline=True
        )
        embed.add_field(
            name="Emoji",
            value=config['emoji'],
            inline=True
        )
        embed.add_field(
            name="Threshold",
            value=str(config['threshold']),
            inline=True
        )
        embed.add_field(
            name="Status",
            value="Locked" if config['locked'] else "Unlocked",
            inline=True
        )
        embed.add_field(
            name="Self Star",
            value="Enabled" if config['allow_selfstar'] else "Disabled",
            inline=True
        )
        embed.add_field(
            name="Color",
            value=config['color'],
            inline=True
        )

        features = []
        if config['show_attachments']:
            features.append("Attachments")
        if config['show_jumpurl']:
            features.append("Jump URL")
        if config['show_timestamp']:
            features.append("Timestamp")

        embed.add_field(
            name="Features",
            value=", ".join(features) if features else "None",
            inline=False
        )

        await ctx.send(embed=embed)

    @clownboard.command(name="reset", brief="Resets guild's configuration for clownboard")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_reset(self, ctx):
        """
        Usage: {0}clownboard reset
        Permission: Manage Guild
        Output:
            Resets the clownboard configuration
            for this server.
        """
        if await ctx.confirm("This will reset all clownboard settings. Continue?"):
            query = """
                    DELETE FROM clownboard_config 
                    WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id)
            
            query = """
                    DELETE FROM clownboard_ignored 
                    WHERE server_id = $1;
                    """
            await self.bot.cxn.execute(query, ctx.guild.id)
            
            # Clear cache
            self.clownboard_cache.pop(ctx.guild.id, None)
            self.ignored_cache.pop(ctx.guild.id, None)
            
            await ctx.success("Clownboard configuration has been reset.")

    @clownboard.command(name="lock", brief="Disables/locks clownboard from operating")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_lock(self, ctx):
        """
        Usage: {0}clownboard lock
        Permission: Manage Guild
        Output:
            Locks the clownboard, preventing
            new messages from being posted.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)
            config = await self.get_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config 
                SET locked = TRUE 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id)
        self.clownboard_cache[ctx.guild.id]['locked'] = True
        
        await ctx.success("Clownboard has been locked.")

    @clownboard.command(name="unlock", brief="Enables/unlocks clownboard from operating")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_unlock(self, ctx):
        """
        Usage: {0}clownboard unlock
        Permission: Manage Guild
        Output:
            Unlocks the clownboard, allowing
            new messages to be posted.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)
            config = await self.get_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config 
                SET locked = FALSE 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id)
        self.clownboard_cache[ctx.guild.id]['locked'] = False
        
        await ctx.success("Clownboard has been unlocked.")

    @clownboard.command(name="set", brief="Sets the channel where clownboard messages will be sent to")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_set(self, ctx, channel: discord.TextChannel):
        """
        Usage: {0}clownboard set <channel>
        Permission: Manage Guild
        Output:
            Sets the clownboard channel where
            clown messages will be posted.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config 
                SET channel_id = $2 
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, channel.id)
        
        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['channel_id'] = channel.id
        
        await ctx.success(f"Clownboard channel has been set to {channel.mention}.")

    @clownboard.command(name="emoji", brief="Sets the emoji that triggers the clownboard messages")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_emoji(self, ctx, emoji):
        """
        Usage: {0}clownboard emoji <emoji>
        Permission: Manage Guild
        Output:
            Sets the emoji that will trigger
            clownboard messages when reacted with.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET emoji = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, str(emoji))

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['emoji'] = str(emoji)

        await ctx.success(f"Clownboard emoji has been set to {emoji}.")

    @clownboard.command(name="threshold", brief="Sets the default amount clowns needed to post")
    @checks.guild_only()
    async def clownboard_threshold(self, ctx, threshold: int):
        """
        Usage: {0}clownboard threshold <threshold>
        Permission: None
        Output:
            Sets the number of clown reactions
            needed for a message to appear on clownboard.
        """
        if threshold < 1:
            return await ctx.fail("Threshold must be at least 1.")

        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET threshold = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, threshold)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['threshold'] = threshold

        await ctx.success(f"Clownboard threshold has been set to {threshold}.")

    @clownboard.command(name="selfstar", brief="Allow an author to clown their own message")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_selfstar(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}clownboard selfstar <setting>
        Permission: Manage Guild
        Output:
            Enables or disables authors from
            clowning their own messages.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET allow_selfstar = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['allow_selfstar'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Self-clowning has been {status}.")

    @clownboard.command(name="attachments", brief="Allow attachments to appear on Clownboard posts")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_attachments(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}clownboard attachments <setting>
        Permission: Manage Guild
        Output:
            Enables or disables attachments
            from appearing on clownboard posts.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET show_attachments = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['show_attachments'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Clownboard attachments have been {status}.")

    @clownboard.command(name="jumpurl", brief="Allow the jump URL to appear on a Clownboard post")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_jumpurl(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}clownboard jumpurl <setting>
        Permission: Manage Guild
        Output:
            Enables or disables jump URLs
            from appearing on clownboard posts.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET show_jumpurl = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['show_jumpurl'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Clownboard jump URLs have been {status}.")

    @clownboard.command(name="timestamp", brief="Allow a timestamp to appear on a Clownboard post")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_timestamp(self, ctx, setting: converters.BoolConverter):
        """
        Usage: {0}clownboard timestamp <setting>
        Permission: Manage Guild
        Output:
            Enables or disables timestamps
            from appearing on clownboard posts.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        query = """
                UPDATE clownboard_config
                SET show_timestamp = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, setting)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['show_timestamp'] = setting

        status = "enabled" if setting else "disabled"
        await ctx.success(f"Clownboard timestamps have been {status}.")

    @clownboard.command(name="color", brief="Set default color for clownboard posts")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_color(self, ctx, *, color):
        """
        Usage: {0}clownboard color <color>
        Permission: Manage Guild
        Output:
            Sets the default color for
            clownboard embed posts.
        """
        config = await self.get_clownboard_config(ctx.guild.id)
        if not config:
            await self.create_clownboard_config(ctx.guild.id)

        # Parse color input
        color_int = await self.parse_color(color)
        if color_int is None:
            return await ctx.fail("Invalid color format. Use hex (#FF0000), decimal (16711680), or role mention.")

        color_hex = f"#{color_int:06x}"
        query = """
                UPDATE clownboard_config
                SET color = $2
                WHERE server_id = $1;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, color_hex)

        if ctx.guild.id not in self.clownboard_cache:
            self.clownboard_cache[ctx.guild.id] = {}
        self.clownboard_cache[ctx.guild.id]['color'] = color_hex

        await ctx.success(f"Clownboard color has been set to {color_hex}.")

    async def parse_color(self, color_input):
        """Parse color input and return integer value"""
        try:
            # Try role conversion first
            role = await commands.RoleConverter().convert(None, color_input)
            return role.color.value
        except:
            pass

        # Parse hex, decimal values
        color_input = str(color_input).strip()

        # Remove common prefixes
        if color_input.startswith('#'):
            color_input = color_input[1:]
        elif color_input.startswith('0x'):
            color_input = color_input[2:]

        try:
            # Try hex conversion
            if all(c in '0123456789abcdefABCDEF' for c in color_input):
                return int(color_input, 16)
        except:
            pass

        try:
            # Try decimal conversion
            color_int = int(color_input)
            if 0 <= color_int <= 0xFFFFFF:
                return color_int
        except:
            pass

        return None

    @clownboard.group(name="ignore", invoke_without_command=True, brief="Ignore a channel, members or roles for new clowns")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_ignore_group(self, ctx, entity: typing.Union[discord.TextChannel, discord.Member, discord.Role] = None):
        """
        Usage: {0}clownboard ignore [channel or member or role]
        Permission: Manage Guild
        Output:
            Ignores the specified channel, member,
            or role from clownboard functionality.
            If no entity is provided, shows help.
        """
        if entity is None:
            return await ctx.send_help(ctx.command)

        entity_type = "channel" if isinstance(entity, discord.TextChannel) else \
                     "user" if isinstance(entity, discord.Member) else "role"

        # Check if already ignored
        if self.is_ignored(ctx.guild.id, entity.id, entity_type):
            return await ctx.fail(f"{entity_type.title()} {entity} is already ignored.")

        query = """
                INSERT INTO clownboard_ignored (server_id, entity_id, entity_type)
                VALUES ($1, $2, $3)
                ON CONFLICT (server_id, entity_id, entity_type) DO NOTHING;
                """
        await self.bot.cxn.execute(query, ctx.guild.id, entity.id, entity_type)

        # Update cache
        if ctx.guild.id not in self.ignored_cache:
            self.ignored_cache[ctx.guild.id] = set()
        self.ignored_cache[ctx.guild.id].add((entity.id, entity_type))

        await ctx.success(f"{entity_type.title()} {entity} has been ignored from clownboard.")

    @clownboard_ignore_group.command(name="list", brief="View ignored roles, members and channels for Clownboard")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_ignore_list(self, ctx):
        """
        Usage: {0}clownboard ignore list
        Permission: Manage Guild
        Output:
            Shows all ignored channels, members,
            and roles for clownboard.
        """
        query = """
                SELECT entity_id, entity_type
                FROM clownboard_ignored
                WHERE server_id = $1;
                """
        records = await self.bot.cxn.fetch(query, ctx.guild.id)

        if not records:
            return await ctx.success("No entities are ignored from clownboard.")

        embed = discord.Embed(
            title="Clownboard Ignored Entities",
            color=0x323339
        )

        channels = []
        users = []
        roles = []

        for record in records:
            entity_id = record['entity_id']
            entity_type = record['entity_type']

            if entity_type == 'channel':
                channel = ctx.guild.get_channel(entity_id)
                if channel:
                    channels.append(channel.mention)
            elif entity_type == 'user':
                user = ctx.guild.get_member(entity_id)
                if user:
                    users.append(str(user))
            elif entity_type == 'role':
                role = ctx.guild.get_role(entity_id)
                if role:
                    roles.append(role.mention)

        if channels:
            embed.add_field(name="Channels", value="\n".join(channels), inline=False)
        if users:
            embed.add_field(name="Users", value="\n".join(users), inline=False)
        if roles:
            embed.add_field(name="Roles", value="\n".join(roles), inline=False)

        if not any([channels, users, roles]):
            embed.description = "All ignored entities have been removed from the server."

        await ctx.send(embed=embed)

    @clownboard.command(name="config", brief="View the settings for clownboard in guild")
    @checks.guild_only()
    @checks.has_perms(manage_guild=True)
    async def clownboard_config(self, ctx):
        """
        Usage: {0}clownboard config
        Permission: Manage Guild
        Output:
            Shows the current clownboard configuration
            for this server in detail.
        """
        # This is the same as the main clownboard command
        await self.clownboard(ctx)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_add(self, payload):
        """Handle clown reactions being added"""
        await self.handle_clown_reaction(payload, added=True)

    @commands.Cog.listener()
    @decorators.wait_until_ready()
    async def on_raw_reaction_remove(self, payload):
        """Handle clown reactions being removed"""
        await self.handle_clown_reaction(payload, added=False)

    async def handle_clown_reaction(self, payload, added=True):
        """Process clown reactions"""
        if not payload.guild_id:
            return

        # Get config
        config = await self.get_clownboard_config(payload.guild_id)
        if not config or not config['channel_id'] or config['locked']:
            return

        # Check if this is the clownboard emoji
        if str(payload.emoji) != config['emoji']:
            return

        guild = self.bot.get_guild(payload.guild_id)
        if not guild:
            return

        channel = guild.get_channel(payload.channel_id)
        if not channel:
            return

        # Don't process reactions in the clownboard channel itself
        if payload.channel_id == config['channel_id']:
            return

        # Check if channel is ignored
        if self.is_ignored(payload.guild_id, payload.channel_id, 'channel'):
            return

        # Check if user is ignored
        if self.is_ignored(payload.guild_id, payload.user_id, 'user'):
            return

        try:
            message = await channel.fetch_message(payload.message_id)
        except discord.NotFound:
            return

        # Check if message author is ignored
        if self.is_ignored(payload.guild_id, message.author.id, 'user'):
            return

        # Check if any of the author's roles are ignored
        if isinstance(message.author, discord.Member):
            for role in message.author.roles:
                if self.is_ignored(payload.guild_id, role.id, 'role'):
                    return

        # Check self-star setting
        if not config['allow_selfstar'] and payload.user_id == message.author.id:
            return

        # Count current clowns
        clown_count = 0
        for reaction in message.reactions:
            if str(reaction.emoji) == config['emoji']:
                clown_count = reaction.count
                break

        # Handle based on clown count and threshold
        if clown_count >= config['threshold']:
            await self.create_or_update_clownboard_message(message, clown_count, config)
        else:
            await self.remove_clownboard_message(message, config)

    async def create_or_update_clownboard_message(self, message, clown_count, config):
        """Create or update a clownboard message"""
        guild = message.guild
        clownboard_channel = guild.get_channel(config['channel_id'])
        if not clownboard_channel:
            return

        # Check if clownboard message already exists
        query = """
                SELECT clownboard_message_id FROM clownboard_messages
                WHERE server_id = $1 AND original_message_id = $2;
                """
        record = await self.bot.cxn.fetchrow(query, guild.id, message.id)

        embed = await self.create_clownboard_embed(message, clown_count, config)

        if record and record['clownboard_message_id']:
            # Update existing message
            try:
                clownboard_message = await clownboard_channel.fetch_message(record['clownboard_message_id'])
                await clownboard_message.edit(embed=embed)

                # Update clown count in database
                query = """
                        UPDATE clownboard_messages
                        SET clown_count = $3
                        WHERE server_id = $1 AND original_message_id = $2;
                        """
                await self.bot.cxn.execute(query, guild.id, message.id, clown_count)
            except discord.NotFound:
                # Clownboard message was deleted, create new one
                await self.create_new_clownboard_message(message, clown_count, config, embed, clownboard_channel)
        else:
            # Create new clownboard message
            await self.create_new_clownboard_message(message, clown_count, config, embed, clownboard_channel)

    async def create_new_clownboard_message(self, message, clown_count, config, embed, clownboard_channel):
        """Create a new clownboard message"""
        try:
            clownboard_message = await clownboard_channel.send(embed=embed)

            # Store in database
            query = """
                    INSERT INTO clownboard_messages
                    (server_id, original_message_id, clownboard_message_id, channel_id, author_id, clown_count)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (server_id, original_message_id)
                    DO UPDATE SET clownboard_message_id = $3, clown_count = $6;
                    """
            await self.bot.cxn.execute(
                query, message.guild.id, message.id, clownboard_message.id,
                message.channel.id, message.author.id, clown_count
            )
        except discord.Forbidden:
            pass  # No permissions to send in clownboard channel

    async def remove_clownboard_message(self, message, config):
        """Remove a clownboard message if clowns fall below threshold"""
        query = """
                SELECT clownboard_message_id FROM clownboard_messages
                WHERE server_id = $1 AND original_message_id = $2;
                """
        record = await self.bot.cxn.fetchrow(query, message.guild.id, message.id)

        if record and record['clownboard_message_id']:
            clownboard_channel = message.guild.get_channel(config['channel_id'])
            if clownboard_channel:
                try:
                    clownboard_message = await clownboard_channel.fetch_message(record['clownboard_message_id'])
                    await clownboard_message.delete()
                except discord.NotFound:
                    pass  # Message already deleted

            # Remove from database
            query = """
                    DELETE FROM clownboard_messages
                    WHERE server_id = $1 AND original_message_id = $2;
                    """
            await self.bot.cxn.execute(query, message.guild.id, message.id)

    async def create_clownboard_embed(self, message, clown_count, config):
        """Create the clownboard embed"""
        embed = discord.Embed(
            color=int(config['color'].replace('#', ''), 16) if config['color'] else 0x323339
        )

        # Set author
        embed.set_author(
            name=f"{message.author.display_name}",
            icon_url=message.author.display_avatar.url
        )

        # Add message content
        content = message.content
        if len(content) > 1024:
            content = content[:1021] + "..."

        if content:
            embed.add_field(name="Message", value=content, inline=False)

        # Add clown count and channel info
        clown_text = f"{config['emoji']} {clown_count}"
        embed.add_field(name="Clowns", value=clown_text, inline=True)
        embed.add_field(name="Channel", value=message.channel.mention, inline=True)

        # Add jump URL if enabled
        if config['show_jumpurl']:
            embed.add_field(name="Jump to Message", value=f"[Click here]({message.jump_url})", inline=True)

        # Add timestamp if enabled
        if config['show_timestamp']:
            embed.timestamp = message.created_at

        # Add image attachment if enabled and exists
        if config['show_attachments'] and message.attachments:
            for attachment in message.attachments:
                if attachment.content_type and attachment.content_type.startswith('image/'):
                    embed.set_image(url=attachment.url)
                    break

        return embed
