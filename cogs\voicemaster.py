import discord
import asyncio
import logging
from discord.ext import commands
from typing import Optional, Union

from utilities import checks
from utilities import converters
from utilities import decorators

logger = logging.getLogger(__name__)

# Embed color matching user preference
EMBED_COLOR = 0x323339


class DisconnectMemberView(discord.ui.View):
    """View for selecting members to disconnect"""

    def __init__(self, members: list, owner: discord.Member):
        super().__init__(timeout=60)
        self.members = members
        self.owner = owner

        # Add dropdown for member selection
        self.add_item(DisconnectMemberSelect(members))


class DisconnectMemberSelect(discord.ui.Select):
    """Dropdown for selecting members to disconnect"""

    def __init__(self, members: list):
        options = []
        for member in members[:25]:  # Discord limit of 25 options
            options.append(discord.SelectOption(
                label=member.display_name,
                description=f"Disconnect {member.display_name}",
                value=str(member.id)
            ))

        super().__init__(
            placeholder="Choose members...",
            min_values=1,
            max_values=min(len(options), 25),
            options=options
        )

    async def callback(self, interaction: discord.Interaction):
        """Handle member selection for disconnection"""
        try:
            disconnected = []
            failed = []

            for member_id in self.values:
                member = interaction.guild.get_member(int(member_id))
                if member and member.voice and member.voice.channel:
                    try:
                        await member.move_to(None)
                        disconnected.append(member.display_name)
                    except:
                        failed.append(member.display_name)

            response_parts = []
            if disconnected:
                response_parts.append(f"✅ Disconnected: {', '.join(disconnected)}")
            if failed:
                response_parts.append(f"❌ Failed to disconnect: {', '.join(failed)}")

            response = "\n".join(response_parts) if response_parts else "❌ No members were disconnected."
            await interaction.response.edit_message(content=response, view=None)

        except Exception as e:
            logger.error(f"Error in disconnect callback: {e}")
            await interaction.response.edit_message(content="❌ An error occurred while disconnecting members.", view=None)


class ActivitySelectionView(discord.ui.View):
    """View for selecting voice channel activities"""

    def __init__(self):
        super().__init__(timeout=60)

    @discord.ui.select(
        placeholder="Select an activity...",
        options=[
            discord.SelectOption(label="Watch Together", description="Watch YouTube videos together", emoji="📺"),
            discord.SelectOption(label="Poker Night", description="Play poker with friends", emoji="🃏"),
            discord.SelectOption(label="Chess", description="Play chess", emoji="♟️"),
            discord.SelectOption(label="Checkers", description="Play checkers", emoji="🔴"),
            discord.SelectOption(label="Betrayal.io", description="Play Betrayal.io", emoji="🔪"),
            discord.SelectOption(label="Fishington.io", description="Play Fishington.io", emoji="🎣"),
            discord.SelectOption(label="Letter League", description="Play Letter League", emoji="📝"),
            discord.SelectOption(label="Word Snacks", description="Play Word Snacks", emoji="🍪"),
            discord.SelectOption(label="Doodle Crew", description="Draw and guess", emoji="🎨"),
            discord.SelectOption(label="Sketch Heads", description="Drawing game", emoji="✏️"),
            discord.SelectOption(label="Spellcast", description="Spelling game", emoji="✨"),
            discord.SelectOption(label="Awkword", description="Word game", emoji="💬"),
            discord.SelectOption(label="Putt Party", description="Mini golf", emoji="⛳"),
        ]
    )
    async def activity_select(self, interaction: discord.Interaction, select: discord.ui.Select):
        """Handle activity selection"""
        selected_activity = select.values[0]

        # Activity IDs for Discord's built-in activities
        activity_ids = {
            "Watch Together": "880218394199220334",
            "Poker Night": "755827207812677713",
            "Chess": "832012774040141894",
            "Checkers": "832013003968348200",
            "Betrayal.io": "773336526917861400",
            "Fishington.io": "814288819477020702",
            "Letter League": "879863686565621790",
            "Word Snacks": "879863976006127627",
            "Doodle Crew": "878067389634314250",
            "Sketch Heads": "902271654783242291",
            "Spellcast": "852509694341283871",
            "Awkword": "879863881349087252",
            "Putt Party": "945737671223947305",
        }

        activity_id = activity_ids.get(selected_activity)
        if not activity_id:
            await interaction.response.edit_message(content="❌ Activity not available.", view=None)
            return

        try:
            # Create activity invite
            channel = interaction.user.voice.channel
            invite = await channel.create_activity_invite(activity_id)

            embed = discord.Embed(
                title=f"🎮 {selected_activity} Started!",
                description=f"Click [here]({invite.url}) to join the activity!",
                color=EMBED_COLOR
            )

            await interaction.response.edit_message(content=None, embed=embed, view=None)

        except Exception as e:
            logger.error(f"Error starting activity: {e}")
            await interaction.response.edit_message(
                content=f"✅ {selected_activity} activity started! (Note: Activity links require Discord's activity feature)",
                view=None
            )


class VoiceMasterInterface(discord.ui.View):
    """VoiceMaster control interface with buttons"""
    
    def __init__(self):
        super().__init__(timeout=None)
        # Make the view persistent
        
    @discord.ui.button(emoji="<:lock:1398025520481960148>", style=discord.ButtonStyle.gray, custom_id="vm_lock")
    async def lock_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Lock the voice channel"""
        await self.handle_voice_action(interaction, "lock")
    
    @discord.ui.button(emoji="<:unlock:1398025533735833720>", style=discord.ButtonStyle.gray, custom_id="vm_unlock")
    async def unlock_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Unlock the voice channel"""
        await self.handle_voice_action(interaction, "unlock")
    
    @discord.ui.button(emoji="<:hide:1398025424432398366>", style=discord.ButtonStyle.gray, custom_id="vm_ghost")
    async def ghost_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Ghost/Hide the voice channel"""
        await self.handle_voice_action(interaction, "ghost")
    
    @discord.ui.button(emoji="<:unhide:1398025523392811090>", style=discord.ButtonStyle.gray, custom_id="vm_reveal")
    async def reveal_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Reveal/Unghost the voice channel"""
        await self.handle_voice_action(interaction, "unghost")
    
    @discord.ui.button(emoji="<:claim:1398025430036119673>", style=discord.ButtonStyle.gray, custom_id="vm_claim")
    async def claim_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Claim the voice channel"""
        await self.handle_voice_action(interaction, "claim")
    
    @discord.ui.button(emoji="<:dc:1398025432078745652>", style=discord.ButtonStyle.gray, custom_id="vm_disconnect", row=1)
    async def disconnect_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Disconnect a member"""
        if not interaction.user.voice or not interaction.user.voice.channel:
            await interaction.response.send_message("❌ You must be in a voice channel to use this button.", ephemeral=True)
            return

        # Get the VoiceMaster cog
        cog = interaction.client.get_cog("VoiceMaster")
        if not cog:
            await interaction.response.send_message("❌ VoiceMaster is not available.", ephemeral=True)
            return

        # Check if user owns the channel
        channel = interaction.user.voice.channel
        is_owner = await cog.is_channel_owner(interaction.user.id, channel.id)

        if not is_owner:
            await interaction.response.send_message("❌ You don't own this voice channel.", ephemeral=True)
            return

        # Get members in the channel (excluding the owner)
        members_in_channel = [m for m in channel.members if m != interaction.user and not m.bot]

        if not members_in_channel:
            await interaction.response.send_message("❌ No members to disconnect from your channel.", ephemeral=True)
            return

        # Create dropdown for member selection
        view = DisconnectMemberView(members_in_channel, interaction.user)
        await interaction.response.send_message("🔗 Select members from the dropdown to disconnect:", view=view, ephemeral=True)

    @discord.ui.button(emoji="<:ac:1398025427569737789>", style=discord.ButtonStyle.gray, custom_id="vm_activity", row=1)
    async def activity_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Start an activity"""
        if not interaction.user.voice or not interaction.user.voice.channel:
            await interaction.response.send_message("❌ You must be in a voice channel to use this button.", ephemeral=True)
            return

        # Get the VoiceMaster cog
        cog = interaction.client.get_cog("VoiceMaster")
        if not cog:
            await interaction.response.send_message("❌ VoiceMaster is not available.", ephemeral=True)
            return

        # Check if user owns the channel
        channel = interaction.user.voice.channel
        is_owner = await cog.is_channel_owner(interaction.user.id, channel.id)

        if not is_owner:
            await interaction.response.send_message("❌ You don't own this voice channel.", ephemeral=True)
            return

        # Create activity selection view
        view = ActivitySelectionView()
        await interaction.response.send_message("🎮 Select an activity to start:", view=view, ephemeral=True)
    
    @discord.ui.button(emoji="<:info:1398026765913292930>", style=discord.ButtonStyle.gray, custom_id="vm_info", row=1)
    async def info_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """View channel information"""
        await self.handle_voice_action(interaction, "info")
    
    @discord.ui.button(emoji="<:inc:1398025535862607884>", style=discord.ButtonStyle.gray, custom_id="vm_increase", row=1)
    async def increase_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Increase user limit"""
        await self.handle_voice_action(interaction, "increase_limit")
    
    @discord.ui.button(emoji="<:dec:1398025434217844847>", style=discord.ButtonStyle.gray, custom_id="vm_decrease", row=1)
    async def decrease_button(self, interaction: discord.Interaction, _: discord.ui.Button):
        """Decrease user limit"""
        await self.handle_voice_action(interaction, "decrease_limit")
    
    async def handle_voice_action(self, interaction: discord.Interaction, action: str):
        """Handle voice channel actions"""
        if not interaction.user.voice or not interaction.user.voice.channel:
            await interaction.response.send_message("❌ You must be in a voice channel to use this button.", ephemeral=True)
            return
        
        # Get the VoiceMaster cog
        cog = interaction.client.get_cog("VoiceMaster")
        if not cog:
            await interaction.response.send_message("❌ VoiceMaster is not available.", ephemeral=True)
            return
        
        # Check if user owns the channel
        channel = interaction.user.voice.channel
        is_owner = await cog.is_channel_owner(interaction.user.id, channel.id)
        
        if not is_owner and action not in ["claim", "info"]:
            await interaction.response.send_message("❌ You don't own this voice channel.", ephemeral=True)
            return
        
        try:
            if action == "lock":
                await cog.lock_channel(channel, interaction.user)
                await interaction.response.send_message("🔒 Voice channel locked.", ephemeral=True)
            
            elif action == "unlock":
                await cog.unlock_channel(channel, interaction.user)
                await interaction.response.send_message("🔓 Voice channel unlocked.", ephemeral=True)
            
            elif action == "ghost":
                await cog.ghost_channel(channel, interaction.user)
                await interaction.response.send_message("👻 Voice channel hidden.", ephemeral=True)
            
            elif action == "unghost":
                await cog.unghost_channel(channel, interaction.user)
                await interaction.response.send_message("👁 Voice channel revealed.", ephemeral=True)
            
            elif action == "claim":
                success = await cog.claim_channel(channel, interaction.user)
                if success:
                    await interaction.response.send_message("🎤 Voice channel claimed.", ephemeral=True)
                else:
                    await interaction.response.send_message("❌ Cannot claim this channel.", ephemeral=True)
            
            elif action == "info":
                embed = await cog.get_channel_info(channel)
                await interaction.response.send_message(embed=embed, ephemeral=True)
            
            elif action == "increase_limit":
                await cog.increase_limit(channel, interaction.user)
                await interaction.response.send_message("➕ User limit increased.", ephemeral=True)
            
            elif action == "decrease_limit":
                await cog.decrease_limit(channel, interaction.user)
                await interaction.response.send_message("➖ User limit decreased.", ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in voice action {action}: {e}")
            await interaction.response.send_message("❌ An error occurred while performing this action.", ephemeral=True)


async def setup(bot):
    await bot.add_cog(VoiceMaster(bot))


class VoiceMaster(commands.Cog):
    """
    Make temporary voice channels in your server.
    """

    def __init__(self, bot):
        self.bot = bot
        # Add persistent view
        self.bot.add_view(VoiceMasterInterface())

    async def is_channel_owner(self, user_id: int, channel_id: int) -> bool:
        """Check if user owns the voice channel"""
        try:
            query = "SELECT owner_id FROM voicemaster_channels WHERE channel_id = $1"
            result = await self.bot.cxn.fetchval(query, channel_id)
            return result == user_id
        except:
            return False

    async def get_channel_settings(self, guild_id: int):
        """Get VoiceMaster settings for a guild"""
        try:
            query = "SELECT * FROM voicemaster_settings WHERE guild_id = $1"
            return await self.bot.cxn.fetchrow(query, guild_id)
        except:
            return None

    async def create_temp_channel(self, member: discord.Member, category: discord.CategoryChannel = None):
        """Create a temporary voice channel for a member"""
        try:
            settings = await self.get_channel_settings(member.guild.id)
            if not settings:
                return None

            # Get default settings and format the name
            default_name_template = settings.get('default_name', "{user}'s Channel")
            default_name = default_name_template.replace("{user}", member.display_name)
            default_limit = settings.get('default_limit', 0)
            default_bitrate = settings.get('default_bitrate', 64000)

            # Create the channel
            overwrites = {
                member.guild.default_role: discord.PermissionOverwrite(connect=True),
                member: discord.PermissionOverwrite(
                    manage_channels=True,
                    manage_permissions=True,
                    move_members=True,
                    mute_members=True,
                    deafen_members=True
                )
            }

            channel = await member.guild.create_voice_channel(
                name=default_name,
                category=category,
                user_limit=default_limit,
                bitrate=default_bitrate,
                overwrites=overwrites
            )

            # Store in database
            query = """
                    INSERT INTO voicemaster_channels (channel_id, owner_id, guild_id, created_at)
                    VALUES ($1, $2, $3, NOW() AT TIME ZONE 'UTC')
                    """
            await self.bot.cxn.execute(query, channel.id, member.id, member.guild.id)

            return channel

        except Exception as e:
            logger.error(f"Error creating temp channel: {e}")
            return None

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        """Handle voice state updates for VoiceMaster"""
        try:
            # Check if member joined a VoiceMaster join channel
            if after.channel:
                settings = await self.get_channel_settings(member.guild.id)
                if settings and after.channel.id == settings.get('join_channel_id'):
                    # Create temporary channel
                    category = None
                    if settings.get('category_id'):
                        category = member.guild.get_channel(settings['category_id'])
                    
                    temp_channel = await self.create_temp_channel(member, category)
                    if temp_channel:
                        await member.move_to(temp_channel)

            # Check if a temporary channel is now empty
            if before.channel:
                query = "SELECT owner_id FROM voicemaster_channels WHERE channel_id = $1"
                result = await self.bot.cxn.fetchval(query, before.channel.id)
                
                if result and len(before.channel.members) == 0:
                    # Delete empty temporary channel
                    await before.channel.delete(reason="VoiceMaster: Empty temporary channel")
                    
                    # Remove from database
                    delete_query = "DELETE FROM voicemaster_channels WHERE channel_id = $1"
                    await self.bot.cxn.execute(delete_query, before.channel.id)

        except Exception as e:
            logger.error(f"Error in voice state update: {e}")

    @decorators.group(
        name="voicemaster",
        aliases=["vm"],
        brief="Make temporary voice channels in your server",
    )
    @checks.cooldown()
    async def _voicemaster(self, ctx):
        if not ctx.invoked_subcommand:
            embed = discord.Embed(
                title="VoiceMaster",
                description="Create and manage temporary voice channels in your server.",
                color=EMBED_COLOR
            )
            embed.add_field(
                name="Getting Started",
                value="Use `voicemaster setup` to configure VoiceMaster for your server",
                inline=False
            )
            embed.add_field(
                name="Popular Commands",
                value="`voicemaster lock` - Lock your channel\n`voicemaster claim` - Claim an inactive channel\n`voicemaster limit <number>` - Set user limit",
                inline=False
            )
            await ctx.send(embed=embed)

    @_voicemaster.command(brief="Begin VoiceMaster server configuration setup")
    @checks.has_perms(manage_guild=True)
    async def setup(self, ctx):
        """
        Usage: {0}voicemaster setup
        Permission: Manage Server
        Output: Configure VoiceMaster for your server
        """
        embed = discord.Embed(
            title="VoiceMaster Setup",
            description="Setting up VoiceMaster for your server...",
            color=EMBED_COLOR
        )

        try:
            # Create VoiceMaster category
            category = await ctx.guild.create_category("VoiceMaster")

            # Create join channel
            join_channel = await ctx.guild.create_voice_channel(
                "Join to Create",
                category=category
            )

            # Store settings in database
            query = """
                    INSERT INTO voicemaster_settings (guild_id, join_channel_id, category_id, default_name, default_limit, default_bitrate)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    ON CONFLICT (guild_id)
                    DO UPDATE SET join_channel_id = $2, category_id = $3
                    """
            await self.bot.cxn.execute(
                query,
                ctx.guild.id,
                join_channel.id,
                category.id,
                "{user}'s Channel",
                0,
                64000
            )

            embed.description = f"✅ VoiceMaster has been set up!\n\n**Join Channel:** {join_channel.mention}\n**Category:** {category.name}"
            embed.add_field(
                name="Next Steps",
                value="Use `voicemaster sendinterface` to send the control panel",
                inline=False
            )

        except Exception as e:
            logger.error(f"Error in VoiceMaster setup: {e}")
            embed.description = "❌ Failed to set up VoiceMaster. Please check my permissions."
            embed.color = 0xff0000

        await ctx.send(embed=embed)

    @_voicemaster.command(brief="Forcefully resend VoiceMaster interface")
    @checks.has_perms(manage_guild=True)
    async def sendinterface(self, ctx):
        """
        Usage: {0}voicemaster sendinterface
        Permission: Manage Server
        Output: Send the VoiceMaster control interface
        """
        embed = discord.Embed(
            description="Use the buttons below to control your voice channel.",
            color=EMBED_COLOR,
        )
        embed.set_author(
            name="hina | voicemaster interface",
            icon_url=self.bot.user.display_avatar.url
        )
        embed.set_thumbnail(url=self.bot.user.display_avatar.url)


        # Add button usage information exactly like the image
        button_info = [
            "<:lock:1398025520481960148> — [`Lock`](https://discord.gg/codexdev) the voice channel",
            "<:unlock:1398025533735833720> — [`Unlock`](https://discord.gg/codexdev) the voice channel",
            "<:hide:1398025424432398366> — [`Hide`](https://discord.gg/codexdev) the voice channel",
            "<:unhide:1398025523392811090> — [`Reveal`](https://discord.gg/codexdev) the voice channel",
            "<:claim:1398025430036119673> — [`Claim`](https://discord.gg/codexdev) the voice channel",
            "<:dc:1398025432078745652> — [`Disconnect`](https://discord.gg/codexdev) a member",
            "<:ac:1398025427569737789> — [`Start`](https://discord.gg/codexdev) an activity",
            "<:info:1398026765913292930> — [`View`](https://discord.gg/codexdev) channel information",
            "<:inc:1398025535862607884> — [`Increase`](https://discord.gg/codexdev) the user limit",
            "<:dec:1398025434217844847> — [`Decrease`](https://discord.gg/codexdev) the user limit"
        ]

        embed.add_field(
            name="Button Usage",
            value="\n".join(button_info),
            inline=False
        )

        view = VoiceMasterInterface()
        await ctx.send(embed=embed, view=view)

    @_voicemaster.command(brief="See current configuration for current Voice Channel")
    async def configuration(self, ctx):
        """
        Usage: {0}voicemaster configuration
        Output: Show current voice channel configuration
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        embed = await self.get_channel_info(channel)
        await ctx.send(embed=embed)

    @_voicemaster.command(brief="Lock your voice channel")
    async def lock(self, ctx):
        """
        Usage: {0}voicemaster lock
        Output: Lock your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await self.lock_channel(channel, ctx.author)
            await ctx.success("🔒 Voice channel locked.")
        except Exception as e:
            logger.error(f"Error locking channel: {e}")
            await ctx.fail("Failed to lock the voice channel.")

    @_voicemaster.command(brief="Unlock your voice channel")
    async def unlock(self, ctx):
        """
        Usage: {0}voicemaster unlock
        Output: Unlock your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await self.unlock_channel(channel, ctx.author)
            await ctx.success("🔓 Voice channel unlocked.")
        except Exception as e:
            logger.error(f"Error unlocking channel: {e}")
            await ctx.fail("Failed to unlock the voice channel.")

    @_voicemaster.command(brief="Hide your voice channel")
    async def ghost(self, ctx):
        """
        Usage: {0}voicemaster ghost
        Output: Hide your voice channel from others
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await self.ghost_channel(channel, ctx.author)
            await ctx.success("👻 Voice channel hidden.")
        except Exception as e:
            logger.error(f"Error ghosting channel: {e}")
            await ctx.fail("Failed to hide the voice channel.")

    @_voicemaster.command(brief="Unhide your voice channel")
    async def unghost(self, ctx):
        """
        Usage: {0}voicemaster unghost
        Output: Unhide your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await self.unghost_channel(channel, ctx.author)
            await ctx.success("👁 Voice channel revealed.")
        except Exception as e:
            logger.error(f"Error unghosting channel: {e}")
            await ctx.fail("Failed to reveal the voice channel.")

    @_voicemaster.command(brief="Claim an inactive voice channel")
    async def claim(self, ctx):
        """
        Usage: {0}voicemaster claim
        Output: Claim ownership of an inactive voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        success = await self.claim_channel(channel, ctx.author)

        if success:
            await ctx.success("🎤 Voice channel claimed.")
        else:
            await ctx.fail("Cannot claim this channel. The owner may still be present.")

    @_voicemaster.command(brief="Set a member limit to your voice channel")
    async def limit(self, ctx, limit: int):
        """
        Usage: {0}voicemaster limit <number>
        Output: Set user limit for your voice channel (0 = no limit)
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        if limit < 0 or limit > 99:
            await ctx.fail("Limit must be between 0 and 99.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await channel.edit(user_limit=limit)
            if limit == 0:
                await ctx.success("🔢 User limit removed.")
            else:
                await ctx.success(f"🔢 User limit set to {limit}.")
        except Exception as e:
            logger.error(f"Error setting limit: {e}")
            await ctx.fail("Failed to set user limit.")

    @_voicemaster.command(brief="Rename your voice channel")
    async def name(self, ctx, *, name: str):
        """
        Usage: {0}voicemaster name <new_name>
        Output: Rename your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        if len(name) > 100:
            await ctx.fail("Channel name must be 100 characters or less.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await channel.edit(name=name)
            await ctx.success(f"📝 Channel renamed to **{name}**.")
        except Exception as e:
            logger.error(f"Error renaming channel: {e}")
            await ctx.fail("Failed to rename the voice channel.")

    @_voicemaster.command(brief="Edit bitrate of your voice channel")
    async def bitrate(self, ctx, bitrate: int):
        """
        Usage: {0}voicemaster bitrate <bitrate>
        Output: Set bitrate for your voice channel (8-384 kbps)
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        if bitrate < 8 or bitrate > 384:
            await ctx.fail("Bitrate must be between 8 and 384 kbps.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await channel.edit(bitrate=bitrate * 1000)  # Convert to bits
            await ctx.success(f"🎵 Bitrate set to {bitrate} kbps.")
        except Exception as e:
            logger.error(f"Error setting bitrate: {e}")
            await ctx.fail("Failed to set bitrate.")

    @_voicemaster.command(brief="Transfer ownership of your channel to another member")
    async def transfer(self, ctx, member: converters.DiscordMember):
        """
        Usage: {0}voicemaster transfer <member>
        Output: Transfer channel ownership to another member
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        if member == ctx.author:
            await ctx.fail("You cannot transfer ownership to yourself.")
            return

        if not member.voice or member.voice.channel != ctx.author.voice.channel:
            await ctx.fail("The member must be in your voice channel.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            # Update database
            query = "UPDATE voicemaster_channels SET owner_id = $1 WHERE channel_id = $2"
            await self.bot.cxn.execute(query, member.id, channel.id)

            # Update permissions
            await channel.set_permissions(ctx.author, overwrite=None)
            await channel.set_permissions(member,
                manage_channels=True,
                manage_permissions=True,
                move_members=True,
                mute_members=True,
                deafen_members=True
            )

            await ctx.success(f"👑 Channel ownership transferred to **{member.display_name}**.")

        except Exception as e:
            logger.error(f"Error transferring ownership: {e}")
            await ctx.fail("Failed to transfer ownership.")

    @_voicemaster.command(brief="Permit a member or role to join your VC")
    async def permit(self, ctx, target: Union[converters.DiscordMember, discord.Role]):
        """
        Usage: {0}voicemaster permit <member/role>
        Output: Allow a member or role to join your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await channel.set_permissions(target, connect=True)
            await ctx.success(f"✅ **{target.name}** can now join your voice channel.")
        except Exception as e:
            logger.error(f"Error permitting access: {e}")
            await ctx.fail("Failed to permit access.")

    @_voicemaster.command(brief="Reject a member or role from joining your VC")
    async def reject(self, ctx, target: Union[converters.DiscordMember, discord.Role]):
        """
        Usage: {0}voicemaster reject <member/role>
        Output: Deny a member or role from joining your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            await channel.set_permissions(target, connect=False)
            # Kick the member if they're in the channel
            if isinstance(target, discord.Member) and target.voice and target.voice.channel == channel:
                await target.move_to(None)

            await ctx.success(f"❌ **{target.name}** can no longer join your voice channel.")
        except Exception as e:
            logger.error(f"Error rejecting access: {e}")
            await ctx.fail("Failed to reject access.")

    # Helper methods for channel operations
    async def lock_channel(self, channel: discord.VoiceChannel, _: discord.Member):
        """Lock a voice channel"""
        await channel.set_permissions(channel.guild.default_role, connect=False)

    async def unlock_channel(self, channel: discord.VoiceChannel, _: discord.Member):
        """Unlock a voice channel"""
        await channel.set_permissions(channel.guild.default_role, connect=True)

    async def ghost_channel(self, channel: discord.VoiceChannel, _: discord.Member):
        """Hide a voice channel"""
        await channel.set_permissions(channel.guild.default_role, view_channel=False)

    async def unghost_channel(self, channel: discord.VoiceChannel, _: discord.Member):
        """Unhide a voice channel"""
        await channel.set_permissions(channel.guild.default_role, view_channel=True)

    async def claim_channel(self, channel: discord.VoiceChannel, member: discord.Member) -> bool:
        """Claim a voice channel"""
        try:
            # Check if current owner is still in the channel
            query = "SELECT owner_id FROM voicemaster_channels WHERE channel_id = $1"
            current_owner_id = await self.bot.cxn.fetchval(query, channel.id)

            if not current_owner_id:
                return False

            current_owner = channel.guild.get_member(current_owner_id)
            if current_owner and current_owner.voice and current_owner.voice.channel == channel:
                return False  # Owner is still present

            # Transfer ownership
            update_query = "UPDATE voicemaster_channels SET owner_id = $1 WHERE channel_id = $2"
            await self.bot.cxn.execute(update_query, member.id, channel.id)

            # Update permissions
            if current_owner:
                await channel.set_permissions(current_owner, overwrite=None)

            await channel.set_permissions(member,
                manage_channels=True,
                manage_permissions=True,
                move_members=True,
                mute_members=True,
                deafen_members=True
            )

            return True

        except Exception as e:
            logger.error(f"Error claiming channel: {e}")
            return False

    async def get_channel_info(self, channel: discord.VoiceChannel) -> discord.Embed:
        """Get channel information embed"""
        embed = discord.Embed(
            title=f"ℹ️ {channel.name}",
            color=EMBED_COLOR
        )

        # Get owner info
        query = "SELECT owner_id FROM voicemaster_channels WHERE channel_id = $1"
        owner_id = await self.bot.cxn.fetchval(query, channel.id)

        if owner_id:
            owner = channel.guild.get_member(owner_id)
            embed.add_field(name="Owner", value=owner.mention if owner else "Unknown", inline=True)

        embed.add_field(name="Members", value=f"{len(channel.members)}", inline=True)
        embed.add_field(name="User Limit", value=f"{channel.user_limit}" if channel.user_limit else "No limit", inline=True)
        embed.add_field(name="Bitrate", value=f"{channel.bitrate // 1000} kbps", inline=True)
        embed.add_field(name="Region", value=str(channel.rtc_region).title() if channel.rtc_region else "Automatic", inline=True)

        # Check permissions
        default_perms = channel.overwrites_for(channel.guild.default_role)
        locked = default_perms.connect is False
        hidden = default_perms.view_channel is False

        status_parts = []
        if locked:
            status_parts.append("🔒 Locked")
        if hidden:
            status_parts.append("👻 Hidden")

        embed.add_field(name="Status", value=" • ".join(status_parts) if status_parts else "🔓 Open", inline=True)

        return embed

    async def increase_limit(self, channel: discord.VoiceChannel, _: discord.Member):
        """Increase channel user limit"""
        current_limit = channel.user_limit
        new_limit = min(current_limit + 1 if current_limit > 0 else 2, 99)
        await channel.edit(user_limit=new_limit)

    async def decrease_limit(self, channel: discord.VoiceChannel, _: discord.Member):
        """Decrease channel user limit"""
        current_limit = channel.user_limit
        if current_limit <= 1:
            new_limit = 0  # No limit
        else:
            new_limit = current_limit - 1
        await channel.edit(user_limit=new_limit)

    @_voicemaster.command(brief="Reset server configuration for VoiceMaster")
    @checks.has_perms(manage_guild=True)
    async def reset(self, ctx):
        """
        Usage: {0}voicemaster reset
        Permission: Manage Server
        Output: Reset VoiceMaster configuration
        """
        if not await ctx.confirm("This will reset all VoiceMaster settings and delete temporary channels. Continue?"):
            return

        try:
            # Get current settings
            settings = await self.get_channel_settings(ctx.guild.id)

            # Delete all temporary channels
            query = "SELECT channel_id FROM voicemaster_channels WHERE guild_id = $1"
            channel_ids = await self.bot.cxn.fetch(query, ctx.guild.id)

            for record in channel_ids:
                channel = ctx.guild.get_channel(record['channel_id'])
                if channel:
                    await channel.delete(reason="VoiceMaster reset")

            # Delete join channel and category if they exist
            if settings:
                if settings.get('join_channel_id'):
                    join_channel = ctx.guild.get_channel(settings['join_channel_id'])
                    if join_channel:
                        await join_channel.delete(reason="VoiceMaster reset")

                if settings.get('category_id'):
                    category = ctx.guild.get_channel(settings['category_id'])
                    if category and len(category.channels) == 0:
                        await category.delete(reason="VoiceMaster reset")

            # Clear database
            await self.bot.cxn.execute("DELETE FROM voicemaster_channels WHERE guild_id = $1", ctx.guild.id)
            await self.bot.cxn.execute("DELETE FROM voicemaster_settings WHERE guild_id = $1", ctx.guild.id)

            await ctx.success("🔄 VoiceMaster has been reset.")

        except Exception as e:
            logger.error(f"Error resetting VoiceMaster: {e}")
            await ctx.fail("Failed to reset VoiceMaster.")

    @_voicemaster.command(brief="Set a status for your voice channel")
    async def status(self, ctx, *, status: str = None):
        """
        Usage: {0}voicemaster status [status]
        Output: Set a status for your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        try:
            if status:
                await channel.edit(status=status)
                await ctx.success(f"📊 Channel status set to: **{status}**")
            else:
                await channel.edit(status=None)
                await ctx.success("📊 Channel status cleared.")
        except Exception as e:
            logger.error(f"Error setting status: {e}")
            await ctx.fail("Failed to set channel status.")

    @_voicemaster.command(brief="Change your channel to a Music Only channel")
    async def music(self, ctx):
        """
        Usage: {0}voicemaster music
        Output: Toggle music-only mode for your voice channel
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        # This is a placeholder - Discord doesn't have a direct "music only" setting
        # You could implement this by restricting speak permissions
        await ctx.success("🎵 Music mode toggled. (Feature in development)")

    @_voicemaster.group(brief="Configure the default settings for VM channels")
    @checks.has_perms(manage_guild=True)
    async def default(self, ctx):
        """Default settings for VoiceMaster channels"""
        if not ctx.invoked_subcommand:
            settings = await self.get_channel_settings(ctx.guild.id)
            if not settings:
                await ctx.fail("VoiceMaster is not set up. Use `voicemaster setup` first.")
                return

            embed = discord.Embed(
                title="VoiceMaster Default Settings",
                color=EMBED_COLOR
            )
            embed.add_field(name="Default Name", value=settings.get('default_name', '{user}\'s Channel'), inline=True)
            embed.add_field(name="Default Limit", value=settings.get('default_limit', 0) or "No limit", inline=True)
            embed.add_field(name="Default Bitrate", value=f"{settings.get('default_bitrate', 64000) // 1000} kbps", inline=True)

            await ctx.send(embed=embed)

    @default.command(name="name", brief="Set default name for new Voice Channels")
    @checks.has_perms(manage_guild=True)
    async def default_name(self, ctx, *, name: str):
        """
        Usage: {0}voicemaster default name <name>
        Permission: Manage Server
        Output: Set default name template for new channels
        Note: Use {user} as placeholder for username
        """
        if len(name) > 100:
            await ctx.fail("Default name must be 100 characters or less.")
            return

        try:
            query = """
                    UPDATE voicemaster_settings
                    SET default_name = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, name, ctx.guild.id)
            await ctx.success(f"📝 Default channel name set to: **{name}**")
        except Exception as e:
            logger.error(f"Error setting default name: {e}")
            await ctx.fail("Failed to set default name.")

    @default.command(name="limit", brief="Set default user limit for new Voice Channels")
    @checks.has_perms(manage_guild=True)
    async def default_limit(self, ctx, limit: int):
        """
        Usage: {0}voicemaster default limit <number>
        Permission: Manage Server
        Output: Set default user limit for new channels (0 = no limit)
        """
        if limit < 0 or limit > 99:
            await ctx.fail("Limit must be between 0 and 99.")
            return

        try:
            query = """
                    UPDATE voicemaster_settings
                    SET default_limit = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, limit, ctx.guild.id)

            if limit == 0:
                await ctx.success("🔢 Default user limit set to: **No limit**")
            else:
                await ctx.success(f"🔢 Default user limit set to: **{limit}**")
        except Exception as e:
            logger.error(f"Error setting default limit: {e}")
            await ctx.fail("Failed to set default limit.")

    @default.command(name="bitrate", brief="Edit default bitrate for new Voice Channels")
    @checks.has_perms(manage_guild=True)
    async def default_bitrate(self, ctx, bitrate: int):
        """
        Usage: {0}voicemaster default bitrate <bitrate>
        Permission: Manage Server
        Output: Set default bitrate for new channels (8-384 kbps)
        """
        if bitrate < 8 or bitrate > 384:
            await ctx.fail("Bitrate must be between 8 and 384 kbps.")
            return

        try:
            query = """
                    UPDATE voicemaster_settings
                    SET default_bitrate = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, bitrate * 1000, ctx.guild.id)
            await ctx.success(f"🎵 Default bitrate set to: **{bitrate} kbps**")
        except Exception as e:
            logger.error(f"Error setting default bitrate: {e}")
            await ctx.fail("Failed to set default bitrate.")

    @default.command(name="region", brief="Edit default region for new Voice Channels")
    @checks.has_perms(manage_guild=True)
    async def default_region(self, ctx, region: str = None):
        """
        Usage: {0}voicemaster default region [region]
        Permission: Manage Server
        Output: Set default region for new channels
        """
        valid_regions = [
            'us-west', 'us-east', 'us-central', 'us-south',
            'singapore', 'southafrica', 'sydney', 'europe',
            'brazil', 'hongkong', 'russia', 'japan', 'india'
        ]

        if region and region.lower() not in valid_regions:
            await ctx.fail(f"Invalid region. Valid regions: {', '.join(valid_regions)}")
            return

        try:
            query = """
                    UPDATE voicemaster_settings
                    SET default_region = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, region.lower() if region else None, ctx.guild.id)

            if region:
                await ctx.success(f"🌍 Default region set to: **{region.title()}**")
            else:
                await ctx.success("🌍 Default region set to: **Automatic**")
        except Exception as e:
            logger.error(f"Error setting default region: {e}")
            await ctx.fail("Failed to set default region.")

    @default.command(name="interface", brief="Send interface to new Voice Channels")
    @checks.has_perms(manage_guild=True)
    async def default_interface(self, ctx, enabled: bool = True):
        """
        Usage: {0}voicemaster default interface [true/false]
        Permission: Manage Server
        Output: Toggle automatic interface sending to new channels
        """
        try:
            query = """
                    UPDATE voicemaster_settings
                    SET interface_enabled = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, enabled, ctx.guild.id)

            if enabled:
                await ctx.success("📱 Interface will be automatically sent to new channels.")
            else:
                await ctx.success("📱 Interface will not be automatically sent to new channels.")
        except Exception as e:
            logger.error(f"Error setting interface setting: {e}")
            await ctx.fail("Failed to update interface setting.")

    @default.command(name="role", brief="Set the default role for hina to set permissions for")
    @checks.has_perms(manage_guild=True)
    async def default_role(self, ctx, role: discord.Role = None):
        """
        Usage: {0}voicemaster default role [role]
        Permission: Manage Server
        Output: Set default role for permission management
        """
        try:
            query = """
                    UPDATE voicemaster_settings
                    SET auto_role = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, role.id if role else None, ctx.guild.id)

            if role:
                await ctx.success(f"👥 Default role set to: **{role.name}**")
            else:
                await ctx.success("👥 Default role cleared.")
        except Exception as e:
            logger.error(f"Error setting default role: {e}")
            await ctx.fail("Failed to set default role.")

    @_voicemaster.command(brief="Redirect voice channels to custom category")
    @checks.has_perms(manage_guild=True)
    async def category(self, ctx, category: discord.CategoryChannel = None):
        """
        Usage: {0}voicemaster category [category]
        Permission: Manage Server
        Output: Set category for temporary voice channels
        """
        try:
            query = """
                    UPDATE voicemaster_settings
                    SET category_id = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, category.id if category else None, ctx.guild.id)

            if category:
                await ctx.success(f"📁 Voice channels will be created in: **{category.name}**")
            else:
                await ctx.success("📁 Voice channels will be created without a specific category.")
        except Exception as e:
            logger.error(f"Error setting category: {e}")
            await ctx.fail("Failed to set category.")

    @_voicemaster.group(brief="Configure the member join settings for VM channels")
    @checks.has_perms(manage_guild=True)
    async def join(self, ctx):
        """Join settings for VoiceMaster channels"""
        if not ctx.invoked_subcommand:
            settings = await self.get_channel_settings(ctx.guild.id)
            if not settings:
                await ctx.fail("VoiceMaster is not set up. Use `voicemaster setup` first.")
                return

            embed = discord.Embed(
                title="VoiceMaster Join Settings",
                color=EMBED_COLOR
            )

            join_role_id = settings.get('join_role')
            join_role = ctx.guild.get_role(join_role_id) if join_role_id else None

            embed.add_field(
                name="Join Role",
                value=join_role.mention if join_role else "None",
                inline=False
            )

            await ctx.send(embed=embed)

    @join.command(name="role", brief="Set a role that members get for being in a VM channel")
    @checks.has_perms(manage_guild=True)
    async def join_role(self, ctx, role: discord.Role = None):
        """
        Usage: {0}voicemaster join role [role]
        Permission: Manage Server
        Output: Set role given to members in VoiceMaster channels
        """
        try:
            query = """
                    UPDATE voicemaster_settings
                    SET join_role = $1, updated_at = NOW() AT TIME ZONE 'UTC'
                    WHERE guild_id = $2
                    """
            await self.bot.cxn.execute(query, role.id if role else None, ctx.guild.id)

            if role:
                await ctx.success(f"👥 Join role set to: **{role.name}**")
            else:
                await ctx.success("👥 Join role cleared.")
        except Exception as e:
            logger.error(f"Error setting join role: {e}")
            await ctx.fail("Failed to set join role.")

    @_voicemaster.command(brief="Grant roles to members who join and remove from members leaving")
    async def role(self, ctx, role: discord.Role):
        """
        Usage: {0}voicemaster role <role>
        Output: Toggle role for your voice channel members
        """
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        # This would require implementing role management on voice state updates
        await ctx.success(f"👥 Role management for **{role.name}** configured. (Feature in development)")

    # Add any missing commands as placeholders
    @_voicemaster.command(brief="Disconnect a member from your voice channel", hidden=True)
    async def disconnect(self, ctx, member: converters.DiscordMember):
        """Disconnect a member from your voice channel"""
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        channel = ctx.author.voice.channel
        is_owner = await self.is_channel_owner(ctx.author.id, channel.id)

        if not is_owner:
            await ctx.fail("You don't own this voice channel.")
            return

        if not member.voice or member.voice.channel != channel:
            await ctx.fail("That member is not in your voice channel.")
            return

        try:
            await member.move_to(None)
            await ctx.success(f"🔗 **{member.display_name}** has been disconnected.")
        except Exception as e:
            logger.error(f"Error disconnecting member: {e}")
            await ctx.fail("Failed to disconnect the member.")

    @_voicemaster.command(brief="Start an activity in your voice channel", hidden=True)
    async def activity(self, ctx, *, activity: str = None):
        """Start an activity in your voice channel"""
        if not ctx.author.voice or not ctx.author.voice.channel:
            await ctx.fail("You must be in a voice channel to use this command.")
            return

        # This would require Discord's activity API integration
        activity_name = activity or "an activity"
        await ctx.success(f"🎮 Starting {activity_name} feature is in development.")
